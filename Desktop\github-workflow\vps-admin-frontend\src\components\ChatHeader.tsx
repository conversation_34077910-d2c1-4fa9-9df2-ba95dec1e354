/**
 * Enhanced chat header component for VPS Admin Chat
 */

import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Sun, Monitor, Settings } from 'lucide-react';
import { motion } from 'framer-motion';
import { useTheme } from '../hooks/useTheme';

interface ChatHeaderProps {
  isTaskActive: boolean;
  taskId: string | null;
  showStatsPanel?: boolean;
  onToggleStats?: () => void;
  className?: string;
  showSettings?: boolean;
  onToggleSettings?: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  isTaskActive,
  taskId,
  showStatsPanel = false,
  onToggleStats,
  className = "",
  showSettings = false,
  onToggleSettings
}) => {
  const { theme, toggleTheme, getThemeIcon, getThemeLabel } = useTheme();

  const getIcon = () => {
    const iconName = getThemeIcon();
    switch (iconName) {
      case 'Sun':
        return <Sun size={14} />;
      case 'Moon':
        return <Moon size={14} />;
      case 'Monitor':
        return <Monitor size={14} />;
      default:
        return <Sun size={14} />;
    }
  };
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-3 border-b border-theme-primary bg-theme-primary flex-shrink-0 flex items-center justify-between transition-colors duration-200 ${className}`}
    >
      {/* Left side - Icon and Title */}
      <div className="flex items-center gap-3 flex-1">
        <motion.div
          whileHover={{ scale: 1.1, rotate: 5 }}
          whileTap={{ scale: 0.95 }}
        >
          <Sparkles size={20} className="text-teal-500"/>
        </motion.div>
        <div className="flex flex-col">
          <h1 className="text-sm lg:text-md font-semibold text-theme-primary">
            VPS Admin
          </h1>
          <span className="text-xs text-theme-secondary hidden lg:block">
            AI-Powered Server Management
          </span>
        </div>
      </div>

      {/* Right side - Controls and Status */}
      <div className="flex items-center gap-2">
        {/* Theme Toggle Button */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={toggleTheme}
          className="p-2 rounded-lg bg-theme-secondary text-theme-secondary hover:bg-theme-tertiary transition-colors"
          title={`Theme: ${getThemeLabel()}`}
        >
          {getIcon()}
        </motion.button>

        {/* Settings Toggle Button */}
        {onToggleSettings && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onToggleSettings}
            className={`p-2 rounded-lg transition-colors ${
              showSettings
                ? 'bg-purple-100 text-purple-700 hover:bg-purple-200'
                : 'bg-theme-secondary text-theme-secondary hover:bg-theme-tertiary'
            }`}
            title="Toggle Settings Panel"
          >
            <Settings size={14} />
          </motion.button>
        )}

        {/* Stats Toggle Button */}
        {onToggleStats && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onToggleStats}
            className={`p-2 rounded-lg transition-colors ${
              showStatsPanel
                ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                : 'bg-theme-secondary text-theme-secondary hover:bg-theme-tertiary'
            }`}
            title="Toggle Statistics Panel"
          >
            <Target size={14} />
          </motion.button>
        )}

        {/* Status Indicator */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className={`text-xs font-medium px-3 py-1.5 rounded-full transition-all duration-200 ${
            isTaskActive
              ? 'bg-green-100 text-green-800 ring-1 ring-green-200'
              : 'bg-theme-secondary text-theme-secondary ring-1 border-theme-primary'
          }`}
        >
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              isTaskActive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
            }`} />
            <span>
              {isTaskActive ? 'Active' : 'Inactive'}
              {taskId && (
                <span className="ml-1 opacity-75">
                  ({taskId.substring(0,6)})
                </span>
              )}
            </span>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ChatHeader;
